import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import 'package:get_it/get_it.dart';
import '../../Farm Setup/services/farm_setup_repository.dart';
// Unused repository import removed
import '../../../utils/message_utils.dart';
import '../../../constants/app_dialog_buttons.dart';
import '../../../constants/app_layout.dart';
import 'dart:math' as math;

// --- Constants ---
class _AppStrings {
  static const String addCattleTitle = 'Add Cattle';
  static const String editCattleTitle = 'Edit Cattle';
  static const String animalTypeLabel = 'Animal Type';
  static const String genderLabel = 'Gender';


  static const String animalTypeRequired = 'Animal Type is required';
  static const String breedRequired = 'Breed is required';
  static const String genderRequired = 'Gender is required';
  static const String sourceRequired = 'Source is required';
  static const String dobRequired =
      'Date of Birth is required for cattle born at farm';
  static const String motherRequired =
      'Mother Tag ID is required for cattle born at farm';
  static const String purchaseDateRequired =
      'Purchase Date is required for purchased cattle';
  static const String purchasePriceRequired =
      'Purchase Price is required for purchased cattle';



  static const String tagIdRequired = 'Please enter a tag ID';
  static const String invalidTagIdFormat =
      'Tag ID must start with animal type first letter followed by numbers';


  // Breed loading messages
  static const String noBreedsAvailable =
      'No breeds available for this animal type';
  static const String errorLoadingBreeds =
      'Error loading breeds. Please try again.';
}

class _AppPadding {
  static const double medium = 16.0;
}

class CattleFormDialog extends StatefulWidget {
  final CattleIsar? cattle;
  final List<AnimalTypeIsar> animalTypes;
  final Function(CattleIsar) onSave;
  final String businessId;

  const CattleFormDialog({
    Key? key,
    this.cattle,
    required this.animalTypes,
    required this.onSave,
    required this.businessId,
  }) : super(key: key);

  @override
  State<CattleFormDialog> createState() => _CattleFormDialogState();
}

class _CattleFormDialogState extends State<CattleFormDialog> {
  final _formKey = GlobalKey<FormState>();
  // Repository removed - using direct Isar operations
  final Isar _isar = GetIt.instance<Isar>();

  // --- Controllers ---
  late TextEditingController _nameController;
  late TextEditingController _tagIdController;
  late TextEditingController _weightController;
  late TextEditingController _colorController;
  late TextEditingController _notesController;
  late TextEditingController _purchasePriceController;

  // --- State Variables ---
  CattleGender? _selectedGender;
  String? _selectedBreedId;
  String? _selectedAnimalTypeId;
  CattleSource? _selectedSource;
  String? _selectedMotherTagId;
  DateTime? _dateOfBirth;
  DateTime? _purchaseDate;
  bool _autoGenerateTagId = true;
  bool _showOptionalFields = false; // Toggle for optional fields visibility
// Store original tag ID for generation logic

  List<BreedCategoryIsar> _filteredBreeds = [];
  bool _isLoadingBreeds = false;
  String? _breedDropdownError; // To show loading/error state in dropdown area

  // Cache for female cattle (for mother dropdown)
  List<CattleIsar> _femaleCattle = [];

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _nameController = TextEditingController();
    _tagIdController = TextEditingController();
    _weightController = TextEditingController();
    _colorController = TextEditingController();
    _notesController = TextEditingController();
    _purchasePriceController = TextEditingController();

    if (widget.cattle != null) {
      // --- Editing existing cattle ---
      final cattle = widget.cattle!;
      _nameController.text = cattle.name ?? '';
      _tagIdController.text = cattle.tagId ?? '';
// Store original enum values
      _selectedGender = cattle.gender;

      // Ensure IDs are strings and valid
      String? initialAnimalTypeId = cattle.animalTypeId?.toString();
      bool animalTypeValid = widget.animalTypes
          .any((type) => type.businessId == initialAnimalTypeId);
      _selectedAnimalTypeId = animalTypeValid ? initialAnimalTypeId : null;

      // Initialize breed handling for editing
      String? initialBreedId = cattle.breedId?.toString();
      _selectedBreedId =
          initialBreedId; // Will be validated by _updateFilteredBreeds

      _dateOfBirth = cattle.dateOfBirth;
      _selectedSource = cattle.source;
      _purchaseDate = cattle.purchaseDate;
      _purchasePriceController.text = cattle.purchasePrice?.toString() ?? '';
      _weightController.text = cattle.weight?.toString() ?? '';
      _colorController.text = cattle.color ?? '';
      _notesController.text = cattle.notes ?? '';
      _selectedMotherTagId = cattle.motherTagId;

      _autoGenerateTagId = false; // Start with auto-generate off when editing

      // Load breeds for the initial animal type
      if (_selectedAnimalTypeId != null) {
        _updateFilteredBreeds(_selectedAnimalTypeId, initialBreedId);
      }
    } else {
      // --- Creating new cattle ---
      // Defaults are null/empty
      _autoGenerateTagId = true; // Start with auto-generate on for new cattle
    }

    // Load female cattle for mother dropdown
    _loadFemaleCattle();
  }

  // --- Event Handlers ---
  void _onAnimalTypeChanged(String? value) {
    if (value == null) return;

    setState(() {
      _selectedAnimalTypeId = value;
      // Reset breed - will be loaded by _updateFilteredBreeds
      _selectedBreedId = null;
      _selectedMotherTagId = null; // Reset mother selection when animal type changes

      if (_autoGenerateTagId) {
        // When changing animal type and auto-generate is on
        _tagIdController.text = '';
      }
    });

    // Load breeds for the new animal type
    _updateFilteredBreeds(value);

    // Reload female cattle for the new animal type
    _loadFemaleCattle();
  }



  Future<void> _handleSave() async {
    debugPrint('DEBUG: _handleSave called');

    if (!_formKey.currentState!.validate()) {
      debugPrint('DEBUG: Form validation failed');
      return;
    }

    // Store context before async operations
    final navigator = Navigator.of(context);
    final scaffoldContext = context;

    try {
      // Validate required fields
      if (_selectedAnimalTypeId == null) {
        debugPrint('DEBUG: Animal type validation failed');
        throw Exception(_AppStrings.animalTypeRequired);
      }
      if (_selectedBreedId == null) {
        debugPrint('DEBUG: Breed validation failed');
        throw Exception(_AppStrings.breedRequired);
      }
      if (_selectedGender == null) {
        debugPrint('DEBUG: Gender validation failed');
        throw Exception(_AppStrings.genderRequired);
      }
      if (_selectedSource == null) {
        debugPrint('DEBUG: Source validation failed');
        throw Exception(_AppStrings.sourceRequired);
      }

      // Source-specific validation
      if (_selectedSource == CattleSource.bornOnFarm) {
        if (_dateOfBirth == null) {
          debugPrint('DEBUG: Date of birth validation failed');
          throw Exception(_AppStrings.dobRequired);
        }
        if (_selectedMotherTagId == null || _selectedMotherTagId!.isEmpty) {
          debugPrint('DEBUG: Mother tag ID validation failed');
          throw Exception(_AppStrings.motherRequired);
        }
      }

      if (_selectedSource == CattleSource.purchased) {
        if (_purchaseDate == null) {
          debugPrint('DEBUG: Purchase date validation failed');
          throw Exception(_AppStrings.purchaseDateRequired);
        }
        if (_purchasePriceController.text.trim().isEmpty) {
          debugPrint('DEBUG: Purchase price validation failed');
          throw Exception(_AppStrings.purchasePriceRequired);
        }
      }

      // Generate new tag ID if auto-generate is on and field is empty
      if (_autoGenerateTagId && _tagIdController.text.trim().isEmpty) {
        _tagIdController.text = await _generateTagId();
        debugPrint('DEBUG: Generated tag ID: ${_tagIdController.text}');
      }

      // Create the cattle object and call the save callback
      final cattle = _createCattleFromForm();

      // Log what we're about to save for debugging
      debugPrint(
          'DEBUG: Saving cattle: ${cattle.businessId}, name: ${cattle.name}, isEdit: ${widget.cattle != null}');

      // Business ID is now generated in _createCattleFromForm() for new cattle

      // Call the save callback but don't navigate away
      widget.onSave(cattle);

      // Only close the dialog, not the entire navigation stack
      if (mounted && navigator.canPop()) {
        debugPrint('DEBUG: Popping dialog only');
        navigator.pop();
      }
    } catch (e) {
      debugPrint('ERROR: Failed to save cattle: $e');
      if (mounted) {
        CattleMessageUtils.showError(scaffoldContext,
            'Error: ${e.toString().replaceAll('Exception: ', '')}');
      }
    }
  }

  CattleIsar _createCattleFromForm() {
    // Create a new cattle object or update the existing one
    final isNewCattle = widget.cattle == null;

    // Create a new CattleIsar instance
    final cattle = CattleIsar();

    // If updating an existing record, copy the necessary data
    if (!isNewCattle && widget.cattle != null) {
      // Copy existing data
      cattle.id = widget.cattle!.id;
      cattle.businessId = widget.cattle!.businessId;
      cattle.createdAt = widget.cattle!.createdAt;
      cattle.status = widget.cattle!.status;

      debugPrint(
          'DEBUG: Preserving IDs for update - id: ${widget.cattle!.id}, businessId: ${widget.cattle!.businessId}');
    }

    // Set the tag ID and other common fields
    cattle.tagId = _tagIdController.text.trim();
    cattle.name = _nameController.text.trim();
    cattle.gender = _selectedGender!;
    cattle.animalTypeId = _selectedAnimalTypeId;
    cattle.breedId = _selectedBreedId;
    cattle.color = _colorController.text.trim();
    cattle.source = _selectedSource!;

    // Set weight if provided
    final weightText = _weightController.text.trim();
    if (weightText.isNotEmpty) {
      cattle.weight = double.tryParse(weightText);
    }

    // Set source-specific fields
    if (_selectedSource == CattleSource.bornOnFarm) {
      cattle.dateOfBirth = _dateOfBirth;
      cattle.motherTagId = _selectedMotherTagId;
    } else if (_selectedSource == CattleSource.purchased) {
      cattle.purchaseDate = _purchaseDate;

      final purchasePrice = _purchasePriceController.text.trim();
      if (purchasePrice.isNotEmpty) {
        cattle.purchasePrice = double.tryParse(purchasePrice);
      }
    }

    cattle.notes = _notesController.text.trim();

    // Set business ID if new
    if (isNewCattle) {
      // Generate unique business ID based on tag ID
      cattle.businessId = CattleIsar.generateBusinessId(cattle.tagId ?? '');
      cattle.createdAt = DateTime.now();
      cattle.status = CattleStatus.active; // Set initial status for new cattle

      // Initialize necessary empty objects to avoid null errors
      cattle.breedingStatus = BreedingStatus();
      cattle.healthInfo = HealthInfo();
      cattle.productionInfo = ProductionInfo();

      debugPrint('DEBUG: Creating new cattle with businessId: ${cattle.businessId}');
    }

    // Always update the updated timestamp
    cattle.updatedAt = DateTime.now();

    // Add debug output to inspect the created cattle object
    debugPrint('DEBUG: Cattle created/updated:');
    debugPrint('  businessId: ${cattle.businessId}');
    debugPrint('  name: ${cattle.name}');
    debugPrint('  tagId: ${cattle.tagId}');
    debugPrint('  animalTypeId: ${cattle.animalTypeId}');
    debugPrint('  breedId: ${cattle.breedId}');
    debugPrint('  gender: ${cattle.gender}');
    debugPrint('  source: ${cattle.source}');

    return cattle;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _tagIdController.dispose();
    _weightController.dispose();
    _colorController.dispose();
    _notesController.dispose();
    _purchasePriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.cattle == null
        ? UniversalFormDialog(
            title: _AppStrings.addCattleTitle,
            headerIcon: Icons.pets, // Cattle-specific icon for add
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelAddRow(
              onCancel: () => Navigator.of(context).pop(),
              onAdd: _handleSave,
            ),
          )
        : UniversalFormDialog(
            title: _AppStrings.editCattleTitle,
            headerIcon: Icons.pets, // Cattle-specific icon for edit
            formContent: _buildFormContent(),
            actionButtons: UniversalDialogButtons.cancelUpdateRow(
              onCancel: () => Navigator.of(context).pop(),
              onUpdate: _handleSave,
            ),
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
                      // Animal Type Dropdown
                      UniversalFormField.dropdownField<String>(
                        label: _AppStrings.animalTypeLabel,
                        value: _selectedAnimalTypeId,
                        items: widget.animalTypes
                            .map<DropdownMenuItem<String>>((type) {
                          return DropdownMenuItem(
                            value: type.businessId,
                            child: Text(type.name ?? 'Unknown Type'),
                          );
                        }).toList(),
                        onChanged: _onAnimalTypeChanged,
                        prefixIcon: Icons.pets,
                        prefixIconColor: Colors.green, // Changed from brown (forbidden)
                        validator: (value) => UniversalFormField.dropdownValidator(value, 'animal type'),
                      ),
                      UniversalFormField.spacing,

                      // Breed Dropdown - only show when animal type is selected
                      if (_selectedAnimalTypeId != null)
                        _isLoadingBreeds
                            ? const Center(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: CircularProgressIndicator(),
                                ),
                              )
                            : _breedDropdownError != null
                                ? Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                        color: Colors.red.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                            color: Colors.red.shade200)),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Row(
                                          children: [
                                            Icon(Icons.error_outline,
                                                color: Colors.red),
                                            SizedBox(width: 8),
                                            Text(
                                              'No breeds available',
                                              style: TextStyle(
                                                  color: Colors.red,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'There are no breeds defined for this animal type yet. Please select a different animal type or add a new breed.',
                                          style: TextStyle(
                                              color: Colors.red.shade800),
                                        ),
                                        const SizedBox(height: 12),
                                        UniversalDialogButtons.clear(
                                          onPressed: () {
                                            // Show dialog/navigation to add a new breed
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                content: Text(
                                                    'To add a new breed, please go to Farm Setup > Breeds'),
                                              ),
                                            );
                                          },
                                          text: 'Add Breed for This Animal Type',
                                        ),
                                      ],
                                    ),
                                  )
                                : UniversalFormField.dropdownField<String>(
                                    label: 'Breed',
                                    value: _selectedBreedId,
                                    items: _filteredBreeds
                                        .map<DropdownMenuItem<String>>((breed) {
                                      return DropdownMenuItem(
                                        value: breed.businessId,
                                        child:
                                            Text(breed.name ?? 'Unknown Breed'),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      setState(() {
                                        _selectedBreedId = value;
                                      });
                                    },
                                    prefixIcon: Icons.category,
                                    prefixIconColor: Colors.deepPurple,
                                    validator: (value) => UniversalFormField.dropdownValidator(value, 'breed'),
                                  ),
                      if (_selectedAnimalTypeId != null)
                        const SizedBox(height: _AppPadding.medium),

                      // Name Field
                      UniversalFormField.textField(
                        label: 'Name',
                        controller: _nameController,
                        prefixIcon: Icons.label,
                        prefixIconColor: Colors.blue,
                        validator: (value) => UniversalFormField.requiredValidator(value, 'name'),
                      ),
                      UniversalFormField.spacing,

                      // Tag ID Field with Auto-generate Toggle
                      UniversalFormField.fieldWithToggle(
                        field: UniversalFormField.textField(
                          label: 'Tag ID',
                          hint: !_autoGenerateTagId
                              ? 'Format: ${_getAnimalTypePrefix()}N (N = number)'
                              : null,
                          controller: _tagIdController,
                          enabled: !_autoGenerateTagId,
                          prefixIcon: Icons.tag,
                          prefixIconColor: Colors.red, // Changed from orange (forbidden)
                          validator: (value) => validateTagId(value),
                        ),
                        toggleLabel: 'Auto',
                        toggleValue: _autoGenerateTagId,
                        onToggleChanged: (value) {
                          setState(() {
                            _autoGenerateTagId = value;
                            if (value) {
                              _tagIdController.text = '';
                            }
                          });
                        },
                      ),
                      UniversalFormField.spacing,

                      // Gender Field
                      UniversalFormField.dropdownField<CattleGender>(
                        label: _AppStrings.genderLabel,
                        value: _selectedGender,
                        items: [CattleGender.male, CattleGender.female]
                            .map<DropdownMenuItem<CattleGender>>(
                              (gender) => DropdownMenuItem(
                                value: gender,
                                child: Text(gender.displayName),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedGender = value;
                          });
                        },
                        prefixIcon: Icons.person,
                        prefixIconColor: Colors.purple,
                        validator: (value) => value == null ? 'Please select a gender' : null,
                      ),
                      UniversalFormField.spacing,

                      // Source Field
                      UniversalFormField.dropdownField<CattleSource>(
                        label: 'Source',
                        value: _selectedSource,
                        items: [
                          DropdownMenuItem(
                              value: CattleSource.purchased, child: Text(CattleSource.purchased.displayName)),
                          DropdownMenuItem(
                              value: CattleSource.bornOnFarm,
                              child: Text(CattleSource.bornOnFarm.displayName)),
                        ],
                        onChanged: (value) {
                          setState(() {
                            if (_selectedSource == CattleSource.bornOnFarm &&
                                value != CattleSource.bornOnFarm) {
                              _selectedMotherTagId = null;
                            }
                            _selectedSource = value;
                            if (value != CattleSource.purchased) {
                              _purchaseDate = null;
                              _purchasePriceController.clear();
                            }
                            if (value != CattleSource.bornOnFarm) {
                              _dateOfBirth = null;
                            }
                          });
                        },
                        prefixIcon: Icons.source,
                        prefixIconColor: Colors.teal,
                        validator: (value) => value == null ? 'Please select a source' : null,
                      ),
                      UniversalFormField.spacing,

                      // Source specific fields
                      if (_selectedSource == CattleSource.bornOnFarm) ...[
                        // Date of Birth
                        UniversalFormField.dateField(
                          context: context,
                          label: 'Date of Birth *',
                          value: _dateOfBirth,
                          onChanged: (date) {
                            setState(() {
                              _dateOfBirth = date;
                            });
                          },
                          prefixIcon: Icons.calendar_today,
                          prefixIconColor: Colors.blue,
                          validator: (date) {
                            if (_selectedSource == CattleSource.bornOnFarm && date == null) {
                              return 'Date of Birth is required';
                            }
                            return null;
                          },
                          lastDate: DateTime.now(),
                        ),
                        UniversalFormField.spacing,

                        // Mother Tag ID
                        if (_selectedAnimalTypeId != null)
                          UniversalFormField.dropdownField<String>(
                            label: 'Mother Tag ID *',
                            value: _selectedMotherTagId,
                            items: _femaleCattle
                                .map<DropdownMenuItem<String>>((cattle) {
                              return DropdownMenuItem(
                                value: cattle.tagId,
                                child: Text(
                                    '${cattle.name ?? "Unknown"} (${cattle.tagId ?? "No Tag"})'),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedMotherTagId = value;
                              });
                            },
                            prefixIcon: Icons.family_restroom,
                            prefixIconColor: Colors.pink, // Changed to avoid color repetition
                            validator: (value) {
                              if (_selectedSource == CattleSource.bornOnFarm &&
                                  (value == null || value.isEmpty)) {
                                return 'Mother Tag ID is required';
                              }
                              return null;
                            },
                          ),
                      ] else if (_selectedSource == CattleSource.purchased) ...[
                        // Purchase Date
                        UniversalFormField.dateField(
                          context: context,
                          label: 'Purchase Date *',
                          value: _purchaseDate,
                          onChanged: (date) {
                            setState(() {
                              _purchaseDate = date;
                            });
                          },
                          prefixIcon: Icons.calendar_today,
                          prefixIconColor: Colors.deepOrange, // Different from birth date
                          validator: (date) {
                            if (_selectedSource == CattleSource.purchased && date == null) {
                              return 'Purchase Date is required';
                            }
                            return null;
                          },
                          lastDate: DateTime.now(),
                        ),
                        UniversalFormField.spacing,

                        // Purchase Price
                        UniversalFormField.numberField(
                          label: 'Purchase Price *',
                          controller: _purchasePriceController,
                          allowDecimals: true,
                          prefixIcon: Icons.attach_money,
                          prefixIconColor: Colors.lightGreen, // Different shade to avoid repetition
                          validator: (value) {
                            if (_selectedSource == CattleSource.purchased &&
                                (value == null || value.trim().isEmpty)) {
                              return 'Purchase Price is required';
                            }
                            return UniversalFormField.numberValidator(value);
                          },
                        ),
                      ],

                      UniversalFormField.sectionSpacing,

                      // Optional Information Toggle Button
                      SizedBox(
                        width: double.infinity,
                        child: OutlinedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showOptionalFields = !_showOptionalFields;
                            });
                          },
                          icon: Icon(
                            _showOptionalFields
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            color: const Color(0xFF2E7D32),
                          ),
                          label: Text(
                            _showOptionalFields
                                ? 'Hide Optional Information'
                                : 'Show Optional Information',
                            style: const TextStyle(
                              color: Color(0xFF2E7D32),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                              color: Color(0xFF2E7D32),
                              width: 1.5,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ),

                      // Optional Fields - Conditionally Displayed
                      if (_showOptionalFields) ...[
                        UniversalFormField.spacing,

                        // Weight Field
                        UniversalFormField.numberField(
                          label: 'Weight (kg)',
                          controller: _weightController,
                          allowDecimals: true,
                          prefixIcon: Icons.monitor_weight,
                          prefixIconColor: Colors.indigo,
                        ),
                        UniversalFormField.spacing,

                        // Color Field
                        UniversalFormField.textField(
                          label: 'Color',
                          controller: _colorController,
                          prefixIcon: Icons.color_lens,
                          prefixIconColor: Colors.deepPurple,
                        ),
                        UniversalFormField.spacing,

                        // Notes Field
                        UniversalFormField.multilineField(
                          label: 'Notes',
                          controller: _notesController,
                          maxLines: 3,
                          prefixIcon: Icons.notes,
                          prefixIconColor: Colors.cyan,
                        ),
                      ],
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  // --- Female Cattle Loading Logic ---
  Future<void> _loadFemaleCattle() async {
    if (!mounted) return;

    try {
      final allCattle = await _isar.cattleIsars.where().findAll();
      final femaleCattle = allCattle.where((cattle) =>
        cattle.gender == CattleGender.female &&
        cattle.animalTypeId == _selectedAnimalTypeId
      ).toList();

      if (mounted) {
        setState(() {
          _femaleCattle = femaleCattle;
        });
      }
    } catch (e) {
      debugPrint('Error loading female cattle: $e');
      if (mounted) {
        setState(() {
          _femaleCattle = [];
        });
      }
    }
  }

  // --- Breed Loading Logic ---
  // This is the single source of truth for breed loading based on animal type
  Future<void> _updateFilteredBreeds(String? animalTypeId,
      [String? initialBreedId]) async {
    if (animalTypeId == null) {
      if (mounted) {
        setState(() {
          _filteredBreeds = [];
          _selectedBreedId = null;
          _isLoadingBreeds = false;
          _breedDropdownError = null;
        });
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoadingBreeds = true;
        _breedDropdownError = null; // Clear previous errors
        _filteredBreeds = []; // Clear previous breeds
      });
    }

    try {
      // Use repository to get all breeds
      final farmSetupRepository = GetIt.instance<FarmSetupRepository>();
      final allBreeds = await farmSetupRepository.getAllBreedCategories();
      final filtered = allBreeds
          .where((breed) => breed.animalTypeId == animalTypeId)
          .toList();

      if (mounted) {
        // Check if initialBreedId is valid within the filtered list
        String? validInitialBreedId = initialBreedId;
        if (initialBreedId != null &&
            !filtered.any((b) => b.businessId == initialBreedId)) {
          debugPrint(
              "Initial breed ID $initialBreedId not found in filtered list for type $animalTypeId. Resetting.");
          validInitialBreedId = null; // Reset if not valid
        }

        setState(() {
          _filteredBreeds = filtered;
          _isLoadingBreeds = false;

          _selectedBreedId ??= validInitialBreedId;

          if (filtered.isEmpty) {
            _breedDropdownError = _AppStrings.noBreedsAvailable;
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading breeds for type $animalTypeId: $e');
      if (mounted) {
        setState(() {
          _isLoadingBreeds = false;
          _filteredBreeds = [];
          _selectedBreedId = null;
          _breedDropdownError = _AppStrings.errorLoadingBreeds;
        });
      }
    }
  }

  // --- Tag ID Logic ---

  Future<String> _generateTagId() async {
    if (_selectedAnimalTypeId == null) {
      return ''; // Should not happen if validation passes
    }

    final animalType = widget.animalTypes.firstWhere(
      (type) => type.businessId == _selectedAnimalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    try {
      // Get all cattle from database to find the highest number for this prefix
      final allCattle = await _isar.cattleIsars.where().findAll();
      int maxNumber = 0;
      final regExp = RegExp('^$prefix(\\d+)\$');

      for (final cattle in allCattle) {
        final tagId = cattle.tagId;
        if (tagId != null && regExp.hasMatch(tagId)) {
          final match = regExp.firstMatch(tagId);
          if (match != null && match.groupCount >= 1) {
            final number = int.tryParse(match.group(1) ?? '0') ?? 0;
            maxNumber = math.max(maxNumber, number);
          }
        }
      }

      // Default approach - simply increment the maximum tag number found
      return '$prefix${maxNumber + 1}';
    } catch (e) {
      debugPrint('Error generating tag ID: $e');
      return '${prefix}1'; // Fallback to prefix + 1
    }
  }

  bool _isValidTagIdFormat(String tagId) {
    if (_selectedAnimalTypeId == null) return false;

    final animalType = widget.animalTypes.firstWhere(
      (type) => type.businessId == _selectedAnimalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    final prefix = animalType.name?.substring(0, 1).toUpperCase() ?? 'X';

    // Format should be: prefix followed by numbers
    final RegExp tagIdRegex = RegExp('^$prefix\\d+\$');
    return tagIdRegex.hasMatch(tagId);
  }

  String _getAnimalTypePrefix() {
    if (_selectedAnimalTypeId == null) return 'X';

    final animalType = widget.animalTypes.firstWhere(
        (type) => type.businessId == _selectedAnimalTypeId,
        orElse: () => AnimalTypeIsar());

    return animalType.name?.substring(0, 1).toUpperCase() ?? 'X';
  }

  // Validates the tag ID format (uniqueness is handled by repository)
  String? validateTagId(String? value) {
    if (!_autoGenerateTagId) {
      // Only validate if not auto-generating
      if (value == null || value.trim().isEmpty) {
        return _AppStrings.tagIdRequired;
      }

      // Check format
      if (!_isValidTagIdFormat(value)) {
        return _AppStrings.invalidTagIdFormat;
      }

      // Note: Duplicate checking is now handled by the repository during save
      // This provides better error handling and consistency
    }
    return null;
  }
}


